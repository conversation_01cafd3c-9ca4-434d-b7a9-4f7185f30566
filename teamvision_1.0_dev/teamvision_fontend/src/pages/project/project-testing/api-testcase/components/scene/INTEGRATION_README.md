# 场景管理组件整合说明

## 概述

已成功将 `SceneList.vue` 和 `SceneForm.vue` 整合到一个组件中，现在 `SceneList.vue` 作为主要的场景管理器，使用 `el-tabs` 组件来管理场景列表和场景表单。

## 主要变更

### 1. 组件结构变更

- **原来**: 两个独立的组件 `SceneList.vue` 和 `SceneForm.vue`
- **现在**: 一个整合的组件 `SceneManager`（文件名仍为 `SceneList.vue`）

### 2. 标签页结构

- **固定标签页**: "全部场景" - 显示场景列表
- **动态标签页**: 新建/编辑场景表单，支持多个同时打开，可关闭

### 3. 功能特性

#### 场景列表功能（保持不变）
- 场景搜索和筛选
- 场景表格展示
- 场景操作（编辑、执行、复制、删除等）
- 分页功能

#### 场景表单功能（整合自 SceneForm.vue）
- 新建场景表单
- 编辑场景表单
- 多标签页内容管理（步骤、参数、脚本、断言、设置等）
- 场景执行和保存功能

### 4. 事件处理

#### 对外事件
- `save-scene(sceneData, mode)` - 保存场景（mode: 'create' | 'edit'）
- `delete-scene(scene)` - 删除场景
- `copy-scene(scene)` - 复制场景
- `execute-scene(scene)` - 执行场景
- `refresh()` - 刷新场景列表

#### 内部事件处理
- 标签页管理（创建、切换、关闭）
- 表单验证和提交
- 场景表单内部标签页切换

## 使用方法

### 在父组件中使用

```vue
<template>
  <scene-manager 
    :project-id="projectID" 
    :scenes="scenes" 
    :loading="loading" 
    @save-scene="handleSaveScene" 
    @delete-scene="handleDeleteScene" 
    @copy-scene="handleCopyScene"
    @execute-scene="handleExecuteScene" 
    @refresh="loadScenes" 
  />
</template>

<script>
import SceneManager from './components/scene/SceneList.vue'

export default {
  components: {
    SceneManager
  },
  methods: {
    handleSaveScene(sceneData, mode) {
      // mode 参数指示是 'create' 还是 'edit'
      if (mode === 'create') {
        // 处理创建逻辑
      } else {
        // 处理更新逻辑
      }
    }
  }
}
</script>
```

### Props

- `projectId` (Number, required) - 项目ID
- `scenes` (Array, default: []) - 场景列表数据
- `loading` (Boolean, default: false) - 加载状态

### 依赖组件

整合后的组件依赖以下子组件：
- `SceneStepsManager` - 步骤管理
- `SceneParamsManager` - 参数管理
- `SceneScriptsManager` - 脚本管理
- `SceneAssertionsManager` - 断言管理
- `SceneExecutionHistory` - 执行历史
- `SceneChangeHistory` - 变更历史
- `SceneSettingsManager` - 设置管理

## 样式特性

- 使用 Element UI 的 `el-tabs` 组件
- 响应式设计，支持移动端
- 保持原有的视觉风格
- 新增场景表单的样式支持

## 注意事项

1. **事件参数变更**: `save-scene` 事件现在包含 `mode` 参数
2. **组件名称**: 虽然文件名仍为 `SceneList.vue`，但组件名已改为 `SceneManager`
3. **标签页管理**: 场景表单现在作为动态标签页，支持多个同时打开
4. **状态管理**: 标签页状态在组件内部管理，不再依赖外部状态管理

## 兼容性

- 保持了原有 `SceneList.vue` 的所有功能
- 整合了 `SceneForm.vue` 的所有功能
- 对外接口基本保持兼容，只有少量事件参数调整
