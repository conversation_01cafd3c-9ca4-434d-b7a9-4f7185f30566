<template>
  <div class="scene-change-history">
    <div class="history-header">
      <h4>变更历史</h4>
      <el-button type="primary" @click="refreshHistory">刷新</el-button>
    </div>

    <el-timeline>
      <el-timeline-item v-for="change in changesList" :key="change.id" :timestamp="change.timestamp" placement="top">
        <el-card>
          <h4>{{ change.action }}</h4>
          <p>{{ change.description }}</p>
          <p class="change-author">操作人: {{ change.author }}</p>
        </el-card>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script>
export default {
  name: 'SceneChangeHistory',
  props: {
    projectId: {
      type: Number,
      required: true
    },
    sceneId: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      changesList: []
    }
  },
  mounted() {
    this.loadHistory()
  },
  methods: {
    loadHistory() {
      // 模拟数据
      this.changesList = [
        {
          id: 1,
          action: '创建场景',
          description: '创建了新的测试场景',
          author: '张三',
          timestamp: '2024-01-15 10:30:00'
        },
        {
          id: 2,
          action: '添加步骤',
          description: '添加了API测试步骤',
          author: '李四',
          timestamp: '2024-01-15 11:00:00'
        }
      ]
    },
    refreshHistory() {
      this.loadHistory()
    }
  }
}
</script>

<style scoped>
.scene-change-history {
  padding: 16px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.history-header h4 {
  margin: 0;
  color: #333;
}

.change-author {
  color: #666;
  font-size: 12px;
  margin: 0;
}
</style>
